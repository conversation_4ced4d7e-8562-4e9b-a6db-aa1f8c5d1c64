from datetime import date

from django.core.validators import MinValueValidator
from django.db import models
from django.forms import ValidationError
from django.utils.translation import gettext_lazy as _
from djmoney.models.fields import MoneyField

from core.mixins import BaseModel, UserAuditModel
from tours.models import TourInstance


class Booking(UserAuditModel):
    """
    Booking model representing a customer's reservation for a tour or service.
    Links clients to tour events with booking details and status tracking.
    """

    class Status(models.TextChoices):
        PENDING = 'pending', _('Pending')
        CONFIRMED = 'confirmed', _('Confirmed')
        CANCELLED = 'cancelled', _('Cancelled')
        COMPLETED = 'completed', _('Completed')
        NO_SHOW = 'no_show', _('No Show')

    class PaymentStatus(models.TextChoices):
        UNPAID = 'unpaid', _('Unpaid')
        DEPOSIT_PAID = 'deposit_paid', _('Deposit Paid')
        FULLY_PAID = 'fully_paid', _('Fully Paid')
        REFUNDED = 'refunded', _('Refunded')

    # Business relationship (for multi-tenancy)
    business = models.ForeignKey(
        'businesses.Business',
        on_delete=models.CASCADE,
        related_name='bookings',
        help_text=_('Business this booking belongs to'),
    )

    # Core relationships
    client = models.ForeignKey(
        'clients.Client',
        on_delete=models.PROTECT,
        related_name='bookings',
        help_text=_('Client making the booking'),
    )
    tour_instance = models.ForeignKey(
        TourInstance,
        on_delete=models.PROTECT,
        related_name='bookings',
        help_text=_('Tour event being booked'),
        null=True,
        blank=True,
    )
    quote = models.ForeignKey(
        'quotes.Quote',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='bookings',
        help_text=_('Associated quote for this booking'),
    )

    # Booking details
    booking_reference = models.CharField(
        max_length=20,
        unique=True,
        help_text=_('Unique booking reference number'),
    )
    number_of_participants = models.PositiveSmallIntegerField(
        default=1,
        validators=[MinValueValidator(1)],
        help_text=_('Number of participants for this booking'),
    )

    # Status and dates
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING,
        help_text=_('Current status of the booking'),
    )
    payment_status = models.CharField(
        max_length=20,
        choices=PaymentStatus,
        default=PaymentStatus.UNPAID,
    )
    booking_date = models.DateTimeField(
        auto_now_add=True,
        help_text=_('Date and time when booking was made'),
    )
    confirmation_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('Date and time when booking was confirmed'),
    )

    # Pricing
    total_amount = MoneyField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Total amount for this booking'),
    )
    deposit_amount = MoneyField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_('Deposit amount paid'),
    )
    deposit_paid = models.BooleanField(
        default=False,
        help_text=_('Whether deposit has been paid'),
    )
    full_payment_received = models.BooleanField(
        default=False,
        help_text=_('Whether full payment has been received'),
    )

    # Additional information
    special_requests = models.TextField(
        blank=True,
        help_text=_('Special requests or notes for this booking'),
    )
    internal_notes = models.TextField(
        blank=True,
        help_text=_('Internal notes (not visible to client)'),
    )

    # Cancellation details
    cancellation_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('Date and time when booking was cancelled'),
    )
    cancellation_reason = models.TextField(
        blank=True,
        help_text=_('Reason for cancellation'),
    )
    refund_amount = MoneyField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_('Amount refunded upon cancellation'),
    )

    class Meta:
        db_table = 'bookings_booking'
        verbose_name = _('Booking')
        verbose_name_plural = _('Bookings')
        indexes = [
            models.Index(fields=['business', 'status']),
            models.Index(fields=['business', 'client']),
            models.Index(fields=['business', 'booking_date']),
            models.Index(fields=['booking_reference']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f'Booking {self.booking_reference} - {self.client.display_name}'

    def save(self, *args, **kwargs):
        """Generate booking reference if not provided."""
        if not self.booking_reference:
            self.booking_reference = self.generate_booking_reference()
        if self.pk is None:
            # only on creation
            if not self.is_available():
                raise ValidationError('Not enough spots available')
        super().save(*args, **kwargs)

    def generate_booking_reference(self):
        """Generate a unique booking reference."""
        import random
        import string

        while True:
            # Generate format: BK-YYYYMMDD-XXXX (e.g., BK-20240131-A1B2)
            date_part = date.today().strftime('%Y%m%d')
            random_part = ''.join(
                random.choices(string.ascii_uppercase + string.digits, k=4)
            )
            reference = f'BK-{date_part}-{random_part}'

            # Check if this reference already exists
            if not Booking.objects.filter(booking_reference=reference).exists():
                return reference

    def is_available(self):
        booked = (
            Booking.objects.filter(
                booking_date=self.booking_date,
                status=self.Status.CONFIRMED,
            ).aggregate(total=models.Sum('num_participants'))['total']
            or 0
        )
        available = (
            self.tour_instance.max_capacity - self.tour_instance.blocked_spots - booked
        )
        return self.tour_instance.max_capacity <= available

    @property
    def is_confirmed(self):
        """Check if booking is confirmed."""
        return self.status == self.Status.CONFIRMED

    @property
    def is_cancelled(self):
        """Check if booking is cancelled."""
        return self.status == self.Status.CANCELLED

    @property
    def is_completed(self):
        """Check if booking is completed."""
        return self.status == self.Status.COMPLETED

    @property
    def outstanding_balance(self):
        """Calculate outstanding balance."""
        paid_amount = self.deposit_amount or 0
        if self.full_payment_received:
            return 0
        return self.total_amount - paid_amount

    @property
    def can_be_cancelled(self):
        """Check if booking can be cancelled."""
        return self.status in [self.Status.PENDING, self.Status.CONFIRMED]

    def confirm_booking(self, user=None):
        """Confirm the booking."""
        from django.utils import timezone

        if self.status == self.Status.PENDING:
            self.status = self.Status.CONFIRMED
            self.confirmation_date = timezone.now()
            self.save()
            return True
        return False

    def cancel_booking(self, reason, refund_amount=None, user=None):
        """Cancel the booking safely."""
        from django.utils import timezone

        if self.can_be_cancelled:
            self.status = self.Status.CANCELLED
            self.cancellation_date = timezone.now()
            self.cancellation_reason = reason
            if refund_amount is not None:
                self.refund_amount = refund_amount
            self.save()
            return True
        return False

    def clean(self):
        # capacity check
        if (
            self.status == self.Status.CONFIRMED
            and self.tour_instance.available_spots < self.number_of_participants
        ):
            raise ValidationError(_('Not enought spots available for this tour date'))

        # payment state enforcement
        if (
            self.payment_status == self.PaymentStatus.FULLY_PAID
            and self.status != self.Status.CONFIRMED
        ):
            raise ValidationError(_('Fully paid bookings must be CONFIRMED'))

        # cancellation rules
        if self.status == self.Status.CANCELLED and not self.cancellation_reason:
            raise ValidationError(_('Cancellation requires a reason'))


class BookingParticipant(UserAuditModel):
    """
    Model to track individual participant details in a booking.
    Links travelers to specific bookings.
    """

    # Core relationships
    booking = models.ForeignKey(
        Booking,
        on_delete=models.CASCADE,
        related_name='participants',
        help_text=_('Booking this participant belongs to'),
    )
    traveler = models.ForeignKey(
        'clients.Traveler',
        on_delete=models.CASCADE,
        related_name='booking_participants',
        help_text=_('Traveler participating in this booking'),
    )

    # Participation details
    is_primary_contact = models.BooleanField(
        default=False,
        help_text=_('Whether this participant is the primary contact'),
    )
    special_requirements = models.TextField(
        blank=True,
        help_text=_('Special requirements for this participant'),
    )

    # Check-in status
    checked_in = models.BooleanField(
        default=False,
        help_text=_('Whether participant has checked in'),
    )
    check_in_time = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('Time when participant checked in'),
    )

    # No-show tracking
    no_show = models.BooleanField(
        default=False,
        help_text=_('Whether participant was a no-show'),
    )

    class Meta:
        db_table = 'bookings_booking_participant'
        verbose_name = _('Booking Participant')
        verbose_name_plural = _('Booking Participants')
        unique_together = [['booking', 'traveler']]
        indexes = [
            models.Index(fields=['booking', 'is_primary_contact']),
            models.Index(fields=['booking', 'checked_in']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f'{self.traveler.full_name} - {self.booking.booking_reference}'

    def check_in(self):
        """Check in the participant."""
        from django.utils import timezone

        if not self.checked_in:
            self.checked_in = True
            self.check_in_time = timezone.now()
            self.no_show = False
            self.save()
            return True
        return False

    def mark_no_show(self):
        """Mark participant as no-show."""
        if not self.checked_in:
            self.no_show = True
            self.save()
            return True
        return False


class BookingAddon(UserAuditModel):
    """
    Links bookings to selected tour add-ons with quantity and pricing.
    """

    booking = models.ForeignKey(
        Booking,
        on_delete=models.CASCADE,
        related_name='addons',
        help_text=_('Booking this addon is for'),
    )
    addon = models.ForeignKey(
        'tours.TourAddon',
        on_delete=models.PROTECT,
        related_name='booking_addons',
        help_text=_('Tour addon selected'),
    )
    quantity = models.PositiveSmallIntegerField(
        default=1,
        validators=[MinValueValidator(1)],
        help_text=_('Quantity of this addon'),
    )
    unit_price = MoneyField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Price per unit at time of booking'),
    )
    total_price = MoneyField(
        max_digits=10,
        decimal_places=2,
        help_text=_('Total price for this addon (quantity × unit_price)'),
    )

    class Meta:
        verbose_name = _('Booking Addon')
        verbose_name_plural = _('Booking Addons')
        unique_together = [['booking', 'addon']]
        indexes = [
            models.Index(fields=['booking']),
            models.Index(fields=['addon']),
        ]

    def __str__(self):
        return (
            f'{self.booking.booking_reference} - {self.addon.name} (×{self.quantity})'
        )

    def save(self, *args, **kwargs):
        """Calculate total price before saving."""
        if not self.total_price:
            self.total_price = self.unit_price * self.quantity
        super().save(*args, **kwargs)

    def clean(self):
        """Validate addon compatibility and quantity limits."""
        super().clean()

        # Check if addon is compatible with the tour
        if hasattr(self.booking, 'tour_instance') and self.booking.tour_instance:
            tour_template = self.booking.tour_instance.tour_template
            if not self.addon.is_compatible_with_tour(tour_template):
                raise ValidationError(
                    _('This addon is not compatible with the selected tour.')
                )

        # Check quantity limits
        if self.addon.max_quantity > 0 and self.quantity > self.addon.max_quantity:
            raise ValidationError(_('Quantity exceeds maximum allowed for this addon.'))


class SpotReservation(BaseModel):
    """
    Temporary reservation of tour spots to prevent double-booking during checkout.
    """

    class Status(models.TextChoices):
        ACTIVE = 'active', _('Active')
        EXPIRED = 'expired', _('Expired')
        CONVERTED = 'converted', _('Converted to Booking')
        CANCELLED = 'cancelled', _('Cancelled')

    tour_instance = models.ForeignKey(
        TourInstance,
        on_delete=models.CASCADE,
        related_name='spot_reservations',
        help_text=_('Tour instance being reserved'),
    )
    session_key = models.CharField(
        max_length=40,
        help_text=_('Session key of the user making the reservation'),
    )
    email = models.EmailField(
        help_text=_('Email of the user making the reservation'),
        blank=True,
    )
    number_of_spots = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1)],
        help_text=_('Number of spots reserved'),
    )
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.ACTIVE,
    )
    expires_at = models.DateTimeField(
        help_text=_('When this reservation expires'),
    )
    booking = models.ForeignKey(
        Booking,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='spot_reservation',
        help_text=_('Booking created from this reservation'),
    )

    class Meta:
        verbose_name = _('Spot Reservation')
        verbose_name_plural = _('Spot Reservations')
        indexes = [
            models.Index(fields=['tour_instance', 'status']),
            models.Index(fields=['session_key']),
            models.Index(fields=['expires_at']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f'Reservation for {self.number_of_spots} spots on {self.tour_instance}'

    @property
    def is_expired(self):
        """Check if the reservation has expired."""
        from django.utils import timezone

        return timezone.now() > self.expires_at

    @property
    def is_active(self):
        """Check if the reservation is still active."""
        return self.status == self.Status.ACTIVE and not self.is_expired

    def extend_expiry(self, minutes=15):
        """Extend the reservation expiry time."""
        from datetime import timedelta

        from django.utils import timezone

        if self.is_active:
            self.expires_at = timezone.now() + timedelta(minutes=minutes)
            self.save()

    def convert_to_booking(self, booking):
        """Convert this reservation to a confirmed booking."""
        self.booking = booking
        self.status = self.Status.CONVERTED
        self.save()

    def cancel(self):
        """Cancel this reservation."""
        self.status = self.Status.CANCELLED
        self.save()

    @classmethod
    def cleanup_expired(cls):
        """Clean up expired reservations."""
        from django.utils import timezone

        expired_reservations = cls.objects.filter(
            status=cls.Status.ACTIVE, expires_at__lt=timezone.now()
        )
        expired_reservations.update(status=cls.Status.EXPIRED)
