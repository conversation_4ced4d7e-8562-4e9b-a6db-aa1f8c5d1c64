# Generated by Django 5.2.4 on 2025-08-13 07:46

import django.db.models.deletion
import djmoney.models.fields
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('businesses', '0001_initial'),
        ('tours', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TourAddon',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('name', models.CharField(help_text='Name of the addon (e.g., "Carbon-fiber trekking poles")', max_length=255, verbose_name='name')),
                ('description', models.TextField(blank=True, help_text='Detailed description of the addon')),
                ('addon_type', models.CharField(choices=[('equipment', 'Equipment'), ('meal', 'Meal'), ('accommodation', 'Accommodation'), ('transport', 'Transport'), ('insurance', 'Insurance'), ('activity', 'Activity'), ('other', 'Other')], default='equipment', max_length=20, verbose_name='addon type')),
                ('price_currency', djmoney.models.fields.CurrencyField(choices=[('AUD', 'Australian Dollar'), ('BRL', 'Brazilian Real'), ('GBP', 'British Pound'), ('CAD', 'Canadian Dollar'), ('CNY', 'Chinese Yuan'), ('EUR', 'Euro'), ('INR', 'Indian Rupee'), ('JPY', 'Japanese Yen'), ('MXN', 'Mexican Peso'), ('CHF', 'Swiss Franc'), ('TZS', 'Tanzanian Shilling'), ('USD', 'US Dollar')], default='USD', editable=False, max_length=3)),
                ('price', djmoney.models.fields.MoneyField(decimal_places=2, default_currency='USD', help_text='Price for this addon', max_digits=10)),
                ('is_available', models.BooleanField(default=True, help_text='Whether this addon is currently available')),
                ('max_quantity', models.PositiveSmallIntegerField(default=1, help_text='Maximum quantity per booking (0 = unlimited)')),
            ],
            options={
                'verbose_name': 'Tour Addon',
                'verbose_name_plural': 'Tour Addons',
            },
        ),
        migrations.AlterModelOptions(
            name='tourtemplate',
            options={'verbose_name': 'Tour Template', 'verbose_name_plural': 'Tour Templates'},
        ),
        migrations.RemoveIndex(
            model_name='tourtemplate',
            name='tours_tourt_created_288067_idx',
        ),
        migrations.AddField(
            model_name='tourtemplate',
            name='accommodation_included',
            field=models.BooleanField(default=False, help_text='Whether accommodation is included'),
        ),
        migrations.AddField(
            model_name='tourtemplate',
            name='activity_type',
            field=models.CharField(choices=[('hiking', 'Hiking'), ('cultural', 'Cultural'), ('adventure', 'Adventure'), ('wildlife', 'Wildlife'), ('photography', 'Photography'), ('cycling', 'Cycling'), ('water_sports', 'Water Sports'), ('climbing', 'Climbing'), ('skiing', 'Skiing'), ('other', 'Other')], default='hiking', help_text='Primary activity type', max_length=20, verbose_name='activity type'),
        ),
        migrations.AddField(
            model_name='tourtemplate',
            name='base_price',
            field=djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='USD', help_text='Base price per person', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='tourtemplate',
            name='base_price_currency',
            field=djmoney.models.fields.CurrencyField(choices=[('AUD', 'Australian Dollar'), ('BRL', 'Brazilian Real'), ('GBP', 'British Pound'), ('CAD', 'Canadian Dollar'), ('CNY', 'Chinese Yuan'), ('EUR', 'Euro'), ('INR', 'Indian Rupee'), ('JPY', 'Japanese Yen'), ('MXN', 'Mexican Peso'), ('CHF', 'Swiss Franc'), ('TZS', 'Tanzanian Shilling'), ('USD', 'US Dollar')], default='USD', editable=False, max_length=3, null=True),
        ),
        migrations.AddField(
            model_name='tourtemplate',
            name='business',
            field=models.ForeignKey(blank=True, help_text='Business this tour template belongs to', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='tour_templates', to='businesses.business'),
        ),
        migrations.AddField(
            model_name='tourtemplate',
            name='destination',
            field=models.CharField(blank=True, help_text='Primary destination (e.g., "Swiss Alps", "Grand Canyon")', max_length=255, verbose_name='destination'),
        ),
        migrations.AddField(
            model_name='tourtemplate',
            name='difficulty_level',
            field=models.CharField(choices=[('easy', 'Easy'), ('moderate', 'Moderate'), ('intermediate', 'Intermediate'), ('challenging', 'Challenging'), ('expert', 'Expert')], default='moderate', help_text='Physical difficulty level', max_length=20, verbose_name='difficulty level'),
        ),
        migrations.AddField(
            model_name='tourtemplate',
            name='equipment_provided',
            field=models.BooleanField(default=False, help_text='Whether equipment is provided'),
        ),
        migrations.AddField(
            model_name='tourtemplate',
            name='exclusions',
            field=models.TextField(blank=True, help_text='What is not included in the tour price'),
        ),
        migrations.AddField(
            model_name='tourtemplate',
            name='inclusions',
            field=models.TextField(blank=True, help_text='What is included in the tour price'),
        ),
        migrations.AddField(
            model_name='tourtemplate',
            name='is_guided',
            field=models.BooleanField(default=True, help_text='Whether the tour includes a guide'),
        ),
        migrations.AddField(
            model_name='tourtemplate',
            name='itinerary',
            field=models.JSONField(default=dict, help_text='Day-by-day itinerary details'),
        ),
        migrations.AddField(
            model_name='tourtemplate',
            name='meals_included',
            field=models.BooleanField(default=False, help_text='Whether meals are included'),
        ),
        migrations.AlterField(
            model_name='tourtemplate',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='tourtemplate',
            name='name',
            field=models.CharField(max_length=255, verbose_name='name'),
        ),
        migrations.AlterUniqueTogether(
            name='tourtemplate',
            unique_together={('business', 'name')},
        ),
        migrations.AddIndex(
            model_name='tourtemplate',
            index=models.Index(fields=['business', 'destination'], name='tours_tourt_busines_adcb5c_idx'),
        ),
        migrations.AddIndex(
            model_name='tourtemplate',
            index=models.Index(fields=['business', 'activity_type'], name='tours_tourt_busines_be0506_idx'),
        ),
        migrations.AddIndex(
            model_name='tourtemplate',
            index=models.Index(fields=['business', 'difficulty_level'], name='tours_tourt_busines_50cadb_idx'),
        ),
        migrations.AddIndex(
            model_name='tourtemplate',
            index=models.Index(fields=['business', 'duration_days'], name='tours_tourt_busines_1711cb_idx'),
        ),
        migrations.AddIndex(
            model_name='tourtemplate',
            index=models.Index(fields=['business', 'is_active'], name='tours_tourt_busines_9124ae_idx'),
        ),
        migrations.AddField(
            model_name='touraddon',
            name='business',
            field=models.ForeignKey(help_text='Business this addon belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='tour_addons', to='businesses.business'),
        ),
        migrations.AddField(
            model_name='touraddon',
            name='compatible_tours',
            field=models.ManyToManyField(blank=True, help_text='Tours this addon is available for (empty = all tours)', related_name='available_addons', to='tours.tourtemplate'),
        ),
        migrations.AddField(
            model_name='touraddon',
            name='created_by',
            field=models.ForeignKey(help_text='User who created this record', on_delete=django.db.models.deletion.PROTECT, related_name='%(class)ss_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='touraddon',
            index=models.Index(fields=['business', 'addon_type'], name='tours_toura_busines_c5f8d0_idx'),
        ),
        migrations.AddIndex(
            model_name='touraddon',
            index=models.Index(fields=['business', 'is_available'], name='tours_toura_busines_9530bf_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='touraddon',
            unique_together={('business', 'name')},
        ),
    ]
