# Generated by Django 5.2.4 on 2025-08-13 09:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bookings', '0002_bookingaddon_spotreservation'),
    ]

    operations = [
        migrations.AddField(
            model_name='booking',
            name='booking_reference_prefix',
            field=models.CharField(blank=True, help_text='Prefix for booking reference (e.g., ALP-ADV for Alpine Adventure)', max_length=10),
        ),
        migrations.AddField(
            model_name='booking',
            name='document_upload_complete',
            field=models.BooleanField(default=False, help_text='Whether all required documents have been uploaded'),
        ),
        migrations.AddField(
            model_name='booking',
            name='waiver_signed',
            field=models.BooleanField(default=False, help_text='Whether the customer has signed the waiver'),
        ),
    ]
