[ ] NAME:Current Task List DESCRIPTION:Root task for conversation **NEW_AGENT** -[x] NAME:Enhance Tour Models for Search and Filtering DESCRIPTION:Add destination, activity, difficulty, and other search fields to TourTemplate model. Create TourAddon model for extras like trekking poles. Update database migrations. -[x] NAME:Create Booking Enhancement Models DESCRIPTION:Create BookingAddon linking model, SpotReservation model for temporary holds, and enhance Client/Booking models with passport, emergency contacts, and dietary preferences. -[/] NAME:Implement Tour Search and Filtering System DESCRIPTION:Create tour search view with filters for destination, activity, duration, difficulty. Include real-time availability display and spot counting. commit the changes using single line commit message. -[ ] NAME:Build Tour Detail Page with Availability Calendar DESCRIPTION:Create comprehensive tour detail view showing itinerary, inclusions/exclusions, real-time availability calendar, and booking initiation. commit the changes using single line commit message. -[ ] NAME:Create Multi-Step Booking Wizard DESCRIPTION:Implement booking wizard with steps: date selection, customization/add-ons, passenger details, and payment. Include temporary spot reservation system. commit the changes using single line commit message. -[ ] NAME:Implement Email Notification System DESCRIPTION:Create email templates for booking confirmation, pre-tour checklists, reminders, and other communications using the existing Herald system. commit the changes using single line commit message. -[ ] NAME:Build Customer Booking Portal DESCRIPTION:Create customer portal for booking management, document uploads, balance payments, date changes, and booking modifications. commit the changes using single line commit message. -[ ] NAME:Create Sample Alpine Adventure Week Tour DESCRIPTION:Set up the specific tour from the scenario with all required data, pricing, and configuration to demonstrate the complete booking flow. commit the changes using single line commit message. -[ ] NAME:Add Real-Time Availability API DESCRIPTION:Create endpoints for real-time availability checking, spot reservation, and dynamic pricing calculations. check the repo: https://github.com/starfederation/datastar-python/blob/develop/src/datastar_py/django.py

commit the changes using single line commit message. -[ ] NAME:Implement payment using lemonsqueezy DESCRIPTION:It is better to avoid vendor locking -[ ] NAME:write behaviour tests and unit test using pytest for the whole project DESCRIPTION:https://testdriven.io/blog/behavior-driven-development-with-django-and-aloe/

commit the changes using single line commit message. -[ ] NAME:a better documentation for the system DESCRIPTION:Update docs, diagrams, and workflows to reflect the actual system progress.

test all workflows, use the model bakery for data generation. Be sure to tie the data to a particular business
